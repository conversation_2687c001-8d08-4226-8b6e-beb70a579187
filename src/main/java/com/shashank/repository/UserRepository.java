package com.shashank.repository;

import com.shashank.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static com.shashank.generated.tables.Users.USERS;

@Slf4j
@Repository
@RequiredArgsConstructor
public class UserRepository {

    private final DSLContext dsl;

    public User save(User user) {
        if (user.getId() == null) {
            user.setId(UUID.randomUUID());
            user.setCreatedAt(LocalDateTime.now());
        }
        user.setUpdatedAt(LocalDateTime.now());

        return dsl.insertInto(USERS)
                .set(USERS.ID, user.getId())
                .set(USERS.USERNAME, user.getUsername())
                .set(USERS.EMAIL, user.getEmail())
                .set(USERS.PASSWORD_HASH, user.getPasswordHash())
                .set(USERS.FIRST_NAME, user.getFirstName())
                .set(USERS.LAST_NAME, user.getLastName())
                .set(USERS.IS_ACTIVE, user.getIsActive())
                .set(USERS.RESET_TOKEN, user.getResetToken())
                .set(USERS.RESET_TOKEN_EXPIRY, user.getResetTokenExpiry())
                .set(USERS.CREATED_AT, user.getCreatedAt())
                .set(USERS.UPDATED_AT, user.getUpdatedAt())
                .returning()
                .fetchOne(record -> User.builder()
                        .id(record.getId())
                        .username(record.getUsername())
                        .email(record.getEmail())
                        .passwordHash(record.getPasswordHash())
                        .firstName(record.getFirstName())
                        .lastName(record.getLastName())
                        .isActive(record.getIsActive())
                        .resetToken(record.getResetToken())
                        .resetTokenExpiry(record.getResetTokenExpiry())
                        .createdAt(record.getCreatedAt())
                        .updatedAt(record.getUpdatedAt())
                        .build());
    }

    public Optional<User> findByUsername(String username) {
        return dsl.selectFrom(USERS)
                .where(USERS.USERNAME.eq(username))
                .fetchOptional(record -> User.builder()
                        .id(record.getId())
                        .username(record.getUsername())
                        .email(record.getEmail())
                        .passwordHash(record.getPasswordHash())
                        .firstName(record.getFirstName())
                        .lastName(record.getLastName())
                        .isActive(record.getIsActive())
                        .resetToken(record.getResetToken())
                        .resetTokenExpiry(record.getResetTokenExpiry())
                        .createdAt(record.getCreatedAt())
                        .updatedAt(record.getUpdatedAt())
                        .build());
    }

    public Optional<User> findByEmail(String email) {
        return dsl.selectFrom(USERS)
                .where(USERS.EMAIL.eq(email))
                .fetchOptional(record -> User.builder()
                        .id(record.getId())
                        .username(record.getUsername())
                        .email(record.getEmail())
                        .passwordHash(record.getPasswordHash())
                        .firstName(record.getFirstName())
                        .lastName(record.getLastName())
                        .isActive(record.getIsActive())
                        .resetToken(record.getResetToken())
                        .resetTokenExpiry(record.getResetTokenExpiry())
                        .createdAt(record.getCreatedAt())
                        .updatedAt(record.getUpdatedAt())
                        .build());
    }

    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        return dsl.selectFrom(USERS)
                .where(USERS.USERNAME.eq(usernameOrEmail)
                        .or(USERS.EMAIL.eq(usernameOrEmail)))
                .fetchOptional(record -> User.builder()
                        .id(record.getId())
                        .username(record.getUsername())
                        .email(record.getEmail())
                        .passwordHash(record.getPasswordHash())
                        .firstName(record.getFirstName())
                        .lastName(record.getLastName())
                        .isActive(record.getIsActive())
                        .resetToken(record.getResetToken())
                        .resetTokenExpiry(record.getResetTokenExpiry())
                        .createdAt(record.getCreatedAt())
                        .updatedAt(record.getUpdatedAt())
                        .build());
    }

    public boolean existsByUsername(String username) {
        return dsl.fetchExists(USERS, USERS.USERNAME.eq(username));
    }

    public boolean existsByEmail(String email) {
        return dsl.fetchExists(USERS, USERS.EMAIL.eq(email));
    }

    public void updateResetToken(UUID userId, String resetToken, LocalDateTime expiry) {
        dsl.update(USERS)
                .set(USERS.RESET_TOKEN, resetToken)
                .set(USERS.RESET_TOKEN_EXPIRY, expiry)
                .set(USERS.UPDATED_AT, LocalDateTime.now())
                .where(USERS.ID.eq(userId))
                .execute();
    }
}
