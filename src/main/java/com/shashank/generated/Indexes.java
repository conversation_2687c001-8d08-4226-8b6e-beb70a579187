/*
 * This file is generated by jOOQ.
 */
package com.shashank.generated;


import com.shashank.generated.tables.Users;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in public.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index IDX_USERS_EMAIL = Internal.createIndex(DSL.name("idx_users_email"), Users.USERS, new OrderField[] { Users.USERS.EMAIL }, false);
    public static final Index IDX_USERS_RESET_TOKEN = Internal.createIndex(DSL.name("idx_users_reset_token"), Users.USERS, new OrderField[] { Users.USERS.RESET_TOKEN }, false);
    public static final Index IDX_USERS_USERNAME = Internal.createIndex(DSL.name("idx_users_username"), Users.USERS, new OrderField[] { Users.USERS.USERNAME }, false);
}
