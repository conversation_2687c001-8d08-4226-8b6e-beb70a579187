/*
 * This file is generated by jOOQ.
 */
package com.shashank.generated;


import com.shashank.generated.tables.Databasechangelog;
import com.shashank.generated.tables.Databasechangeloglock;
import com.shashank.generated.tables.Users;


/**
 * Convenience access to all tables in public.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>public.databasechangelog</code>.
     */
    public static final Databasechangelog DATABASECHANGELOG = Databasechangelog.DATABASECHANGELOG;

    /**
     * The table <code>public.databasechangeloglock</code>.
     */
    public static final Databasechangeloglock DATABASECHANGELOGLOCK = Databasechangeloglock.DATABASECHANGELOGLOCK;

    /**
     * The table <code>public.users</code>.
     */
    public static final Users USERS = Users.USERS;
}
