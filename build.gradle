plugins {
    id 'java'
    id 'org.springframework.boot' version '2.6.6'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'nu.studer.jooq' version '7.1.1'
    id 'org.liquibase.gradle' version '2.1.1'
}

group = 'com.shashank'
version = '1.0-SNAPSHOT'
sourceCompatibility = '11'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    // Database
    implementation 'org.postgresql:postgresql'
    implementation 'org.liquibase:liquibase-core'

    // jOOQ
    implementation 'org.springframework.boot:spring-boot-starter-jooq'
    jooqGenerator 'org.postgresql:postgresql'

    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

// jOOQ Configuration
jooq {
    version = '3.16.6'
    edition = nu.studer.gradle.jooq.JooqEdition.OSS

    configurations {
        main {
            generateSchemaSourceOnCompilation = true

            generationTool {
                logging = org.jooq.meta.jaxb.Logging.WARN
                jdbc {
                    driver = 'org.postgresql.Driver'
                    url = '*****************************************'
                    user = 'postgres'
                    password = 'password'
                }
                generator {
                    name = 'org.jooq.codegen.DefaultGenerator'
                    database {
                        name = 'org.jooq.meta.postgres.PostgresDatabase'
                        inputSchema = 'public'
                    }
                    generate {
                        pojos = true
                        daos = true
                        springAnnotations = true
                    }
                    target {
                        packageName = 'com.shashank.generated'
                        directory = 'src/main/java'
                    }
                }
            }
        }
    }
}

// Liquibase Configuration
liquibase {
    activities {
        main {
            changeLogFile 'src/main/resources/db/changelog/db.changelog-master.yml'
            url '*****************************************'
            username 'postgres'
            password 'password'
            driver 'org.postgresql.Driver'
        }
    }
}

test {
    useJUnitPlatform()
}

// Ensure jOOQ generation happens after Liquibase update
generateJooq.dependsOn update