/*
 * This file is generated by jOOQ.
 */
package com.shashank.generated.tables.records;


import com.shashank.generated.tables.Users;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UsersRecord extends UpdatableRecordImpl<UsersRecord> implements Record11<UUID, String, String, String, String, String, Boolean, String, LocalDateTime, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.users.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.users.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.users.username</code>.
     */
    public void setUsername(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.users.username</code>.
     */
    public String getUsername() {
        return (String) get(1);
    }

    /**
     * Setter for <code>public.users.email</code>.
     */
    public void setEmail(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.users.email</code>.
     */
    public String getEmail() {
        return (String) get(2);
    }

    /**
     * Setter for <code>public.users.password_hash</code>.
     */
    public void setPasswordHash(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.users.password_hash</code>.
     */
    public String getPasswordHash() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.users.first_name</code>.
     */
    public void setFirstName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.users.first_name</code>.
     */
    public String getFirstName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.users.last_name</code>.
     */
    public void setLastName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.users.last_name</code>.
     */
    public String getLastName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.users.is_active</code>.
     */
    public void setIsActive(Boolean value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.users.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(6);
    }

    /**
     * Setter for <code>public.users.reset_token</code>.
     */
    public void setResetToken(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.users.reset_token</code>.
     */
    public String getResetToken() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.users.reset_token_expiry</code>.
     */
    public void setResetTokenExpiry(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.users.reset_token_expiry</code>.
     */
    public LocalDateTime getResetTokenExpiry() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.users.created_at</code>.
     */
    public void setCreatedAt(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.users.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>public.users.updated_at</code>.
     */
    public void setUpdatedAt(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.users.updated_at</code>.
     */
    public LocalDateTime getUpdatedAt() {
        return (LocalDateTime) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, String, String, String, String, String, Boolean, String, LocalDateTime, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, String, String, String, String, String, Boolean, String, LocalDateTime, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Users.USERS.ID;
    }

    @Override
    public Field<String> field2() {
        return Users.USERS.USERNAME;
    }

    @Override
    public Field<String> field3() {
        return Users.USERS.EMAIL;
    }

    @Override
    public Field<String> field4() {
        return Users.USERS.PASSWORD_HASH;
    }

    @Override
    public Field<String> field5() {
        return Users.USERS.FIRST_NAME;
    }

    @Override
    public Field<String> field6() {
        return Users.USERS.LAST_NAME;
    }

    @Override
    public Field<Boolean> field7() {
        return Users.USERS.IS_ACTIVE;
    }

    @Override
    public Field<String> field8() {
        return Users.USERS.RESET_TOKEN;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return Users.USERS.RESET_TOKEN_EXPIRY;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return Users.USERS.CREATED_AT;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return Users.USERS.UPDATED_AT;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getUsername();
    }

    @Override
    public String component3() {
        return getEmail();
    }

    @Override
    public String component4() {
        return getPasswordHash();
    }

    @Override
    public String component5() {
        return getFirstName();
    }

    @Override
    public String component6() {
        return getLastName();
    }

    @Override
    public Boolean component7() {
        return getIsActive();
    }

    @Override
    public String component8() {
        return getResetToken();
    }

    @Override
    public LocalDateTime component9() {
        return getResetTokenExpiry();
    }

    @Override
    public LocalDateTime component10() {
        return getCreatedAt();
    }

    @Override
    public LocalDateTime component11() {
        return getUpdatedAt();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getUsername();
    }

    @Override
    public String value3() {
        return getEmail();
    }

    @Override
    public String value4() {
        return getPasswordHash();
    }

    @Override
    public String value5() {
        return getFirstName();
    }

    @Override
    public String value6() {
        return getLastName();
    }

    @Override
    public Boolean value7() {
        return getIsActive();
    }

    @Override
    public String value8() {
        return getResetToken();
    }

    @Override
    public LocalDateTime value9() {
        return getResetTokenExpiry();
    }

    @Override
    public LocalDateTime value10() {
        return getCreatedAt();
    }

    @Override
    public LocalDateTime value11() {
        return getUpdatedAt();
    }

    @Override
    public UsersRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public UsersRecord value2(String value) {
        setUsername(value);
        return this;
    }

    @Override
    public UsersRecord value3(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public UsersRecord value4(String value) {
        setPasswordHash(value);
        return this;
    }

    @Override
    public UsersRecord value5(String value) {
        setFirstName(value);
        return this;
    }

    @Override
    public UsersRecord value6(String value) {
        setLastName(value);
        return this;
    }

    @Override
    public UsersRecord value7(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public UsersRecord value8(String value) {
        setResetToken(value);
        return this;
    }

    @Override
    public UsersRecord value9(LocalDateTime value) {
        setResetTokenExpiry(value);
        return this;
    }

    @Override
    public UsersRecord value10(LocalDateTime value) {
        setCreatedAt(value);
        return this;
    }

    @Override
    public UsersRecord value11(LocalDateTime value) {
        setUpdatedAt(value);
        return this;
    }

    @Override
    public UsersRecord values(UUID value1, String value2, String value3, String value4, String value5, String value6, Boolean value7, String value8, LocalDateTime value9, LocalDateTime value10, LocalDateTime value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UsersRecord
     */
    public UsersRecord() {
        super(Users.USERS);
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(UUID id, String username, String email, String passwordHash, String firstName, String lastName, Boolean isActive, String resetToken, LocalDateTime resetTokenExpiry, LocalDateTime createdAt, LocalDateTime updatedAt) {
        super(Users.USERS);

        setId(id);
        setUsername(username);
        setEmail(email);
        setPasswordHash(passwordHash);
        setFirstName(firstName);
        setLastName(lastName);
        setIsActive(isActive);
        setResetToken(resetToken);
        setResetTokenExpiry(resetTokenExpiry);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(com.shashank.generated.tables.pojos.Users value) {
        super(Users.USERS);

        if (value != null) {
            setId(value.getId());
            setUsername(value.getUsername());
            setEmail(value.getEmail());
            setPasswordHash(value.getPasswordHash());
            setFirstName(value.getFirstName());
            setLastName(value.getLastName());
            setIsActive(value.getIsActive());
            setResetToken(value.getResetToken());
            setResetTokenExpiry(value.getResetTokenExpiry());
            setCreatedAt(value.getCreatedAt());
            setUpdatedAt(value.getUpdatedAt());
        }
    }
}
