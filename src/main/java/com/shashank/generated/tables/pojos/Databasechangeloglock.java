/*
 * This file is generated by jOOQ.
 */
package com.shashank.generated.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Databasechangeloglock implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer       id;
    private Boolean       locked;
    private LocalDateTime lockgranted;
    private String        lockedby;

    public Databasechangeloglock() {}

    public Databasechangeloglock(Databasechangeloglock value) {
        this.id = value.id;
        this.locked = value.locked;
        this.lockgranted = value.lockgranted;
        this.lockedby = value.lockedby;
    }

    public Databasechangeloglock(
        Integer       id,
        Boolean       locked,
        LocalDateTime lockgranted,
        String        lockedby
    ) {
        this.id = id;
        this.locked = locked;
        this.lockgranted = lockgranted;
        this.lockedby = lockedby;
    }

    /**
     * Getter for <code>public.databasechangeloglock.id</code>.
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * Setter for <code>public.databasechangeloglock.id</code>.
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for <code>public.databasechangeloglock.locked</code>.
     */
    public Boolean getLocked() {
        return this.locked;
    }

    /**
     * Setter for <code>public.databasechangeloglock.locked</code>.
     */
    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    /**
     * Getter for <code>public.databasechangeloglock.lockgranted</code>.
     */
    public LocalDateTime getLockgranted() {
        return this.lockgranted;
    }

    /**
     * Setter for <code>public.databasechangeloglock.lockgranted</code>.
     */
    public void setLockgranted(LocalDateTime lockgranted) {
        this.lockgranted = lockgranted;
    }

    /**
     * Getter for <code>public.databasechangeloglock.lockedby</code>.
     */
    public String getLockedby() {
        return this.lockedby;
    }

    /**
     * Setter for <code>public.databasechangeloglock.lockedby</code>.
     */
    public void setLockedby(String lockedby) {
        this.lockedby = lockedby;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Databasechangeloglock (");

        sb.append(id);
        sb.append(", ").append(locked);
        sb.append(", ").append(lockgranted);
        sb.append(", ").append(lockedby);

        sb.append(")");
        return sb.toString();
    }
}
