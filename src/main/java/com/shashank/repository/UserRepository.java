package com.shashank.repository;

import com.shashank.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Repository
public class UserRepository {

    // Temporary in-memory storage for demo purposes
    private final Map<UUID, User> users = new HashMap<>();
    private final Map<String, UUID> usernameIndex = new HashMap<>();
    private final Map<String, UUID> emailIndex = new HashMap<>();

    public User save(User user) {
        if (user.getId() == null) {
            user.setId(UUID.randomUUID());
            user.setCreatedAt(LocalDateTime.now());
        }
        user.setUpdatedAt(LocalDateTime.now());

        // Store in memory maps
        users.put(user.getId(), user);
        usernameIndex.put(user.getUsername(), user.getId());
        emailIndex.put(user.getEmail(), user.getId());

        log.info("User saved: {}", user.getUsername());
        return user;
    }

    public Optional<User> findByUsername(String username) {
        UUID userId = usernameIndex.get(username);
        return userId != null ? Optional.of(users.get(userId)) : Optional.empty();
    }

    public Optional<User> findByEmail(String email) {
        UUID userId = emailIndex.get(email);
        return userId != null ? Optional.of(users.get(userId)) : Optional.empty();
    }

    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        // Try username first, then email
        Optional<User> user = findByUsername(usernameOrEmail);
        return user.isPresent() ? user : findByEmail(usernameOrEmail);
    }

    public boolean existsByUsername(String username) {
        return usernameIndex.containsKey(username);
    }

    public boolean existsByEmail(String email) {
        return emailIndex.containsKey(email);
    }

    public void updateResetToken(UUID userId, String resetToken, LocalDateTime expiry) {
        User user = users.get(userId);
        if (user != null) {
            user.setResetToken(resetToken);
            user.setResetTokenExpiry(expiry);
            user.setUpdatedAt(LocalDateTime.now());
            log.info("Reset token updated for user: {}", userId);
        }
    }
}
