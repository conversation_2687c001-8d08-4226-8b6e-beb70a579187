/*
 * This file is generated by jOOQ.
 */
package com.shashank.generated.tables.daos;


import com.shashank.generated.tables.Databasechangeloglock;
import com.shashank.generated.tables.records.DatabasechangeloglockRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DatabasechangeloglockDao extends DAOImpl<DatabasechangeloglockRecord, com.shashank.generated.tables.pojos.Databasechangeloglock, Integer> {

    /**
     * Create a new DatabasechangeloglockDao without any configuration
     */
    public DatabasechangeloglockDao() {
        super(Databasechangeloglock.DATABASECHANGELOGLOCK, com.shashank.generated.tables.pojos.Databasechangeloglock.class);
    }

    /**
     * Create a new DatabasechangeloglockDao with an attached configuration
     */
    @Autowired
    public DatabasechangeloglockDao(Configuration configuration) {
        super(Databasechangeloglock.DATABASECHANGELOGLOCK, com.shashank.generated.tables.pojos.Databasechangeloglock.class, configuration);
    }

    @Override
    public Integer getId(com.shashank.generated.tables.pojos.Databasechangeloglock object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchRangeOfId(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(Databasechangeloglock.DATABASECHANGELOGLOCK.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchById(Integer... values) {
        return fetch(Databasechangeloglock.DATABASECHANGELOGLOCK.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.shashank.generated.tables.pojos.Databasechangeloglock fetchOneById(Integer value) {
        return fetchOne(Databasechangeloglock.DATABASECHANGELOGLOCK.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchOptionalById(Integer value) {
        return fetchOptional(Databasechangeloglock.DATABASECHANGELOGLOCK.ID, value);
    }

    /**
     * Fetch records that have <code>locked BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchRangeOfLocked(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>locked IN (values)</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchByLocked(Boolean... values) {
        return fetch(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKED, values);
    }

    /**
     * Fetch records that have <code>lockgranted BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchRangeOfLockgranted(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKGRANTED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>lockgranted IN (values)</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchByLockgranted(LocalDateTime... values) {
        return fetch(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKGRANTED, values);
    }

    /**
     * Fetch records that have <code>lockedby BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchRangeOfLockedby(String lowerInclusive, String upperInclusive) {
        return fetchRange(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKEDBY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>lockedby IN (values)</code>
     */
    public List<com.shashank.generated.tables.pojos.Databasechangeloglock> fetchByLockedby(String... values) {
        return fetch(Databasechangeloglock.DATABASECHANGELOGLOCK.LOCKEDBY, values);
    }
}
