plugins {
    id 'java'
    id 'org.springframework.boot' version '2.6.6'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'nu.studer.jooq' version '7.1.1'
    id 'org.liquibase.gradle' version '2.2.0'
}

group = 'com.shashank'
version = '1.0-SNAPSHOT'
sourceCompatibility = '17'

// Main class configuration
springBoot {
    mainClass = 'com.shashank.UserAuthApplication'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

sourceSets {
    main {
        java {
            srcDirs += 'build/generated-src/jooq/main'
        }
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    // Database
    implementation 'org.postgresql:postgresql'
    implementation 'org.liquibase:liquibase-core'
    liquibaseRuntime 'org.liquibase:liquibase-core'
    liquibaseRuntime 'org.postgresql:postgresql'
    liquibaseRuntime 'info.picocli:picocli:4.6.3'
    liquibaseRuntime 'org.yaml:snakeyaml:1.30'

    // jOOQ
    implementation 'org.springframework.boot:spring-boot-starter-jooq'
    jooqGenerator 'org.postgresql:postgresql'
    jooqGenerator 'jakarta.xml.bind:jakarta.xml.bind-api:3.0.1'
    jooqGenerator 'org.glassfish.jaxb:jaxb-runtime:3.0.2'

    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

// jOOQ Configuration
jooq {
    version = '3.16.6'
    edition = nu.studer.gradle.jooq.JooqEdition.OSS

    configurations {
        main {
            generateSchemaSourceOnCompilation = true

            generationTool {
                logging = org.jooq.meta.jaxb.Logging.WARN
                jdbc {
                    driver = 'org.postgresql.Driver'
                    url = '*****************************************'
                    user = 'postgres'
                    password = 'password'
                }
                generator {
                    name = 'org.jooq.codegen.DefaultGenerator'
                    database {
                        name = 'org.jooq.meta.postgres.PostgresDatabase'
                        inputSchema = 'public'
                    }
                    generate {
                        pojos = true
                        daos = true
                        springAnnotations = true
                    }
                    target {
                        packageName = 'com.shashank.generated'
                        directory = 'build/generated-src/jooq/main'
                    }
                }
            }
        }
    }
}

// Liquibase Configuration
liquibase {
    activities {
        main {
            changelogFile 'src/main/resources/db/changelog/db.changelog-master.yml'
            url '*****************************************'
            username 'postgres'
            password 'password'
            driver 'org.postgresql.Driver'
            classpath 'src/main/resources'
        }
    }
}

test {
    useJUnitPlatform()
}

// Ensure jOOQ generation happens after Liquibase update
generateJooq.dependsOn update