version: '3.3'

services:
  postgres:
    image: postgres:13
    container_name: userauth-postgres
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: userauth
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: on-failure
    networks:
      - userauth-network

volumes:
  postgres_data:

networks:
  userauth-network:
    external: true
